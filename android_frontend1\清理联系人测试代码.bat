@echo off
chcp 65001 >nul
echo ========================================
echo 清理APK联系人测试代码
echo ========================================
echo.

echo 🔍 正在检查需要清理的文件...
echo.

REM 列出需要删除的联系人测试文件
set "files_to_delete=直接注入代码.js 网站通讯录测试代码.js 网站嵌入代码.html 简化版注入代码.js"
set "files_to_delete=%files_to_delete% 权限调试测试页面.html APK功能测试页面.html 一键测试页面.html"
set "files_to_delete=%files_to_delete% 嵌入dailuanshej网站的测试代码.html"

echo 📋 将要删除的测试文件：
for %%f in (%files_to_delete%) do (
    if exist "%%f" (
        echo   ✅ %%f
    ) else (
        echo   ❌ %%f ^(不存在^)
    )
)
echo.

echo ⚠️  警告：这些文件包含联系人测试代码，可能导致APK登录时弹跳到联系人页面
echo.

set /p confirm=确认删除这些测试文件吗？(Y/N): 
if /i "%confirm%" neq "Y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🗑️  开始删除测试文件...
echo.

REM 删除测试文件
for %%f in (%files_to_delete%) do (
    if exist "%%f" (
        del "%%f" >nul 2>&1
        if !errorlevel! equ 0 (
            echo   ✅ 已删除: %%f
        ) else (
            echo   ❌ 删除失败: %%f
        )
    )
)

echo.
echo 📁 检查文档文件中的联系人相关内容...

REM 列出包含联系人内容的文档文件（仅提示，不删除）
set "doc_files=APK通讯录修复指南.md 通讯录功能测试指南.md 通讯录测试完整指南.md"
set "doc_files=%doc_files% 通讯录读取停住问题修复说明.md 权限弹窗修复指南.md"
set "doc_files=%doc_files% 权限弹窗无响应完整解决方案.md 权限弹窗无响应解决方案.md"
set "doc_files=%doc_files% 权限弹窗调试指南.md"

echo.
echo 📄 发现以下文档文件包含联系人相关内容（保留作为历史记录）：
for %%f in (%doc_files%) do (
    if exist "%%f" (
        echo   📋 %%f
    )
)

echo.
echo ✅ 清理完成！
echo.
echo 📝 重要提醒：
echo   1. 已删除所有联系人测试JavaScript文件
echo   2. 已删除所有联系人测试HTML页面
echo   3. 文档文件已保留作为历史记录
echo   4. 请确保服务器上也删除了这些测试文件
echo.
echo 🚀 下一步操作：
echo   1. 重新构建APK
echo   2. 部署到服务器时不要包含测试文件
echo   3. 测试APK登录是否还会弹跳到联系人页面
echo.

pause
