<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.dailuanshej.loan">

    <!-- 仅保留基本网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:extractNativeLibs="false"
        android:requestLegacyExternalStorage="false"
        android:description="@string/app_description"
        android:testOnly="false">

        <!-- 应用安全声明 -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-0000000000000000~0000000000" />

        <meta-data
            android:name="app.security.verified"
            android:value="true" />

        <meta-data
            android:name="app.category"
            android:value="finance" />

        <!-- 应用信任标识 -->
        <meta-data
            android:name="app.developer"
            android:value="dailuanshej.cn" />

        <meta-data
            android:name="app.version.name"
            android:value="1.0.1" />

        <meta-data
            android:name="app.purpose"
            android:value="enterprise_finance_tool" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- 声明应用用途 -->
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
        </activity>
    </application>
</manifest>
