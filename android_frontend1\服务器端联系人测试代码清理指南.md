# 服务器端联系人测试代码清理指南

## 问题描述

APK登录时弹跳到读取联系人页面，原因是服务器上部署了联系人测试JavaScript代码，导致网页自动触发联系人功能。

## 需要从服务器删除的文件

### 🗑️ JavaScript测试文件
```
直接注入代码.js
网站通讯录测试代码.js
简化版注入代码.js
```

### 🗑️ HTML测试页面
```
网站嵌入代码.html
权限调试测试页面.html
APK功能测试页面.html
一键测试页面.html
嵌入dailuanshej网站的测试代码.html
```

## 服务器清理步骤

### 1. 登录服务器
```bash
# 通过SSH或FTP登录到 https://dailuanshej.cn 服务器
```

### 2. 检查网站根目录
```bash
# 检查是否存在这些测试文件
ls -la | grep -E "(直接注入|网站通讯录|网站嵌入|权限调试|APK功能|一键测试|嵌入dailuanshej)"
```

### 3. 删除测试文件
```bash
# 删除JavaScript测试文件
rm -f 直接注入代码.js
rm -f 网站通讯录测试代码.js
rm -f 简化版注入代码.js

# 删除HTML测试页面
rm -f 网站嵌入代码.html
rm -f 权限调试测试页面.html
rm -f APK功能测试页面.html
rm -f 一键测试页面.html
rm -f 嵌入dailuanshej网站的测试代码.html
```

### 4. 检查主页面
检查网站主页面（如 index.php, index.html）是否包含以下内容：

**需要删除的代码片段：**
```javascript
// 任何包含以下关键词的代码
AndroidContacts
readAllContacts
requestContactsPermission
onContactsResult
onContactsPermissionResult
通讯录测试
联系人测试
```

**需要删除的HTML元素：**
```html
<!-- 任何包含联系人测试的按钮或面板 -->
<button>测试通讯录</button>
<div id="contacts-test-panel">
<div id="contacts-test-ui">
```

## 验证清理结果

### 1. 浏览器测试
- 在浏览器中访问 https://dailuanshej.cn
- 按F12打开开发者工具
- 检查控制台是否有联系人相关的日志
- 检查页面是否有联系人测试按钮

### 2. APK测试
- 重新构建并安装APK
- 登录测试是否还会弹跳到联系人页面
- 检查是否还有权限请求弹窗

## 预防措施

### 1. 部署检查清单
在部署新版本时，确保：
- ✅ 不包含任何测试JavaScript文件
- ✅ 不包含任何测试HTML页面
- ✅ 主页面代码中没有联系人测试代码
- ✅ 没有自动执行的联系人权限请求

### 2. 代码审查
在代码中搜索以下关键词，确保没有遗漏：
```
AndroidContacts
readAllContacts
requestContactsPermission
通讯录
联系人
contacts-test
permission-test
```

### 3. 测试环境隔离
- 测试代码只在开发环境使用
- 生产环境严禁部署测试代码
- 使用不同的域名或子域名进行测试

## 常见问题

### Q: 清理后APK还是跳转到联系人页面？
A: 检查以下几点：
1. 服务器缓存是否已清理
2. CDN缓存是否已刷新
3. 浏览器缓存是否已清理
4. APK是否使用了最新版本

### Q: 如何确认服务器上的文件已完全删除？
A: 
1. 使用 `find` 命令搜索相关文件
2. 检查网站访问日志
3. 使用浏览器开发者工具检查网络请求

### Q: 误删了重要文件怎么办？
A: 
1. 从备份中恢复
2. 重新部署正确的网站文件
3. 只删除明确标识为测试的文件

## 联系支持

如果清理过程中遇到问题，请联系技术支持并提供：
1. 服务器访问日志
2. 浏览器开发者工具截图
3. APK测试结果

---

**重要提醒**：清理完成后，请重新测试APK登录流程，确保不再出现联系人页面弹跳问题。
