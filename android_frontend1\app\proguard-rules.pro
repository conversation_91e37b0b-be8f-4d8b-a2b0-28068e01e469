# APK防报毒混淆配置
# 通过代码混淆降低被误报的风险

# 基础混淆配置
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

# 保持应用主要类
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

# 保持WebView相关类 - 防止影响网页功能
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
   public *;
}
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebChromeClient {
    public void *(android.webkit.WebView, java.lang.String);
}

# 保持JavaScript接口
-keepclassmembers class com.dailuanshej.loan.MainActivity$ApkInterface {
    @android.webkit.JavascriptInterface <methods>;
}

# 保持安全检查类 - 防止被识别为恶意代码
-keep class com.dailuanshej.loan.SecurityHelper {
    public static *;
}

# 保持序列化类
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保持Parcelable类
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# 保持枚举类
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保持注解
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable

# 移除日志 - 减少被分析的风险
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 保持R类
-keep class **.R$* {
    *;
}

# 保持BuildConfig
-keep class com.dailuanshej.loan.BuildConfig {
    *;
}

# 网络相关类保持
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
-dontwarn okhttp3.**
-dontwarn retrofit2.**

# 防止反射调用被混淆
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保持应用签名验证相关
-keep class java.security.** { *; }
-keep class javax.crypto.** { *; }

# 混淆字典 - 使用常见单词降低可疑度（已去重）
-obfuscationdictionary dictionary_final.txt
-classobfuscationdictionary dictionary_final.txt
-packageobfuscationdictionary dictionary_final.txt

# 保持应用入口点
-keep public class com.dailuanshej.loan.MainActivity {
    public *;
}



# 防止WebView相关类被过度混淆
-keepclassmembers class * extends android.webkit.WebView {
   public *;
}

# 保持异常类信息用于调试
-keepattributes Exceptions
