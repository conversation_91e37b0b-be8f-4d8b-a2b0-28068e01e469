package com.dailuanshej.loan;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import org.json.JSONObject;

/**
 * 主Activity - 已移除所有联系人相关功能
 * 仅保留基本的WebView功能和APK专用接口
 */
public class MainActivity extends AppCompatActivity {
    private static final String TAG = "YouYiHua";
    private WebView webView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Log.d(TAG, "=== MainActivity启动 ===");

        initWebView();
        loadWebsite();

        Log.d(TAG, "=== MainActivity初始化完成 ===");
    }

    /**
     * 初始化WebView
     */
    private void initWebView() {
        webView = findViewById(R.id.webView);

        // WebView基本设置
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setDisplayZoomControls(false);

        // 安全性设置
        webSettings.setAllowFileAccess(false);
        webSettings.setAllowContentAccess(false);
        webSettings.setAllowFileAccessFromFileURLs(false);
        webSettings.setAllowUniversalAccessFromFileURLs(false);
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_NEVER_ALLOW);

        // 设置用户代理
        webSettings.setUserAgentString(webSettings.getUserAgentString() + " YouYiHua/1.0 (Official)");

        // 注册APK专用功能接口
        webView.addJavascriptInterface(new ApkInterface(this), "Android");

        Log.d(TAG, "✅ JavaScript接口已注册：Android");

        // 设置WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                // 只允许加载官方域名
                if (url.startsWith("https://dailuanshej.cn")) {
                    return false;  // 允许加载
                }
                Log.w(TAG, "阻止加载非官方URL: " + url);
                return true;  // 阻止加载
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "页面加载完成: " + url);

                // 注入安全标识和移动端文字修复
                view.evaluateJavascript(
                    "window.APP_SECURITY_VERIFIED = true; " +
                    "window.APP_VERSION = '1.0'; " +
                    "console.log('优易花官方APP已验证');",
                    null);

                // 延迟注入，确保页面DOM完全加载
                new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                    // 注入移动端文字横排修复
                    injectMobileTextFix();
                }, 1000);
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "页面加载错误: " + description);
            }
        });

        Log.d(TAG, "WebView初始化完成");
    }

    /**
     * 加载网站
     */
    private void loadWebsite() {
        String websiteUrl = "https://dailuanshej.cn/";
        Log.d(TAG, "开始加载网站: " + websiteUrl);
        webView.loadUrl(websiteUrl);
    }

    /**
     * 注入移动端文字横排修复CSS
     */
    private void injectMobileTextFix() {
        Log.d(TAG, "注入移动端文字横排修复CSS");
        
        String fixScript = 
            "try {" +
            "  var style = document.createElement('style');" +
            "  style.textContent = '" +
            "    * { writing-mode: horizontal-tb !important; text-orientation: mixed !important; }" +
            "    input, textarea, select { writing-mode: horizontal-tb !important; }" +
            "    .form-control, .form-group { writing-mode: horizontal-tb !important; }" +
            "    table, td, th { writing-mode: horizontal-tb !important; }" +
            "  ';" +
            "  document.head.appendChild(style);" +
            "  console.log('✅ 移动端文字横排修复已应用');" +
            "} catch(e) {" +
            "  console.error('❌ 移动端文字修复失败:', e);" +
            "}";
            
        webView.evaluateJavascript(fixScript, value -> {
            Log.d(TAG, "移动端文字修复注入完成");
        });
    }

    private String getWebViewVersion() {
        try {
            android.content.pm.PackageInfo info = getPackageManager().getPackageInfo("com.google.android.webview", 0);
            return info.versionName;
        } catch (android.content.pm.PackageManager.NameNotFoundException e) {
            return "N/A";
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    /**
     * APK专用功能接口 - 支持合同打印和分享
     */
    public class ApkInterface {
        private Context context;

        public ApkInterface(Context context) {
            this.context = context;
        }

        @JavascriptInterface
        public void shareContract(String title, String url) {
            Log.d(TAG, "分享合同: " + title + " - " + url);

            try {
                Intent shareIntent = new Intent(Intent.ACTION_SEND);
                shareIntent.setType("text/plain");
                shareIntent.putExtra(Intent.EXTRA_SUBJECT, title);
                shareIntent.putExtra(Intent.EXTRA_TEXT, "合同链接：" + url);

                Intent chooser = Intent.createChooser(shareIntent, "分享合同");
                if (shareIntent.resolveActivity(context.getPackageManager()) != null) {
                    context.startActivity(chooser);
                } else {
                    Log.e(TAG, "没有找到可用的分享应用");
                }
            } catch (Exception e) {
                Log.e(TAG, "分享合同失败: " + e.getMessage());
            }
        }

        @JavascriptInterface
        public void printContract(String contractHtml) {
            Log.d(TAG, "打印合同请求");

            runOnUiThread(() -> {
                try {
                    // 检查Android版本，新版本支持WebView打印
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
                        // 创建打印适配器
                        android.print.PrintDocumentAdapter printAdapter = webView.createPrintDocumentAdapter("contract");
                        android.print.PrintManager printManager = (android.print.PrintManager) getSystemService(Context.PRINT_SERVICE);
                        
                        // 创建打印作业
                        String jobName = "优易花合同打印";
                        printManager.print(jobName, printAdapter, new android.print.PrintAttributes.Builder().build());
                    } else {
                        // 旧版本Android的处理
                        showPrintAlternatives();
                    }
                } catch (Exception e) {
                    Log.e(TAG, "打印失败: " + e.getMessage());
                    showPrintAlternatives();
                }
            });
        }

        private void showPrintAlternatives() {
            runOnUiThread(() -> {
                android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(MainActivity.this);
                builder.setTitle("📱 打印选项")
                       .setMessage("请选择打印方式：\n\n1️⃣ 截图保存\n2️⃣ 分享到其他应用\n3️⃣ 在浏览器中打开")
                       .setPositiveButton("截图保存", (dialog, which) -> takeScreenshot())
                       .setNeutralButton("分享", (dialog, which) -> shareCurrentPage())
                       .setNegativeButton("取消", null)
                       .show();
            });
        }

        private void takeScreenshot() {
            // 实现截图功能
            Log.d(TAG, "执行截图保存");
            Toast.makeText(context, "截图功能开发中...", Toast.LENGTH_SHORT).show();
        }

        private void shareCurrentPage() {
            // 分享当前页面
            Log.d(TAG, "分享当前页面");
            try {
                Intent shareIntent = new Intent(Intent.ACTION_SEND);
                shareIntent.setType("text/plain");
                shareIntent.putExtra(Intent.EXTRA_SUBJECT, "优易花合同");
                shareIntent.putExtra(Intent.EXTRA_TEXT, "请查看合同内容");
                context.startActivity(Intent.createChooser(shareIntent, "分享合同"));
            } catch (Exception e) {
                Log.e(TAG, "分享失败: " + e.getMessage());
            }
        }

        @JavascriptInterface
        public String getAppInfo() {
            try {
                org.json.JSONObject info = new org.json.JSONObject();
                info.put("appName", "优易花");
                info.put("appVersion", "1.0.1");
                info.put("platform", "Android");
                info.put("webViewVersion", getWebViewVersion());
                info.put("androidVersion", android.os.Build.VERSION.RELEASE);
                info.put("sdkVersion", android.os.Build.VERSION.SDK_INT);
                info.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date()));

                return info.toString();
            } catch (Exception e) {
                Log.e(TAG, "获取应用信息失败", e);
                return "{}";
            }
        }

        @JavascriptInterface
        public void showToast(String message) {
            runOnUiThread(() -> {
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
            });
        }

        @JavascriptInterface
        public void logMessage(String message) {
            Log.d(TAG, "JS日志: " + message);
        }
    }
}
